package vn.com.mbbank.kanban.mbmonitor.external.execution.services;

import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionApiResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionApiEntity;

/**
 * ExecutionApiService.
 */
public interface ExecutionApiService  extends BaseService<ExecutionApiEntity, String> {

  /**
   * find ExecutionApiEntity by executionId.
   *
   * @param executionId to find
   * @return ExecutionApiEntity
   */
  ExecutionApiEntity findAllByExecutionId(String executionId);

  /**
   * call api.
   *
   * @param executionApiInfo to call rest template
   * @return ExecutionApiResponse
   */
  ExecutionApiResponse callExecutionApi(ExecutionApiEntity executionApiInfo);
}
