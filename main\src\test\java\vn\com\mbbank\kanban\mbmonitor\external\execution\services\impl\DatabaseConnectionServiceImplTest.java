package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.DatabaseConnectionRepository;

@ExtendWith(MockitoExtension.class)
class DatabaseConnectionServiceImplTest {

  @Mock
  private DatabaseConnectionRepository databaseConnectionRepository;

  @InjectMocks
  private DatabaseConnectionServiceImpl databaseConnectionService;

  @Test
  void getRepository_success_returnsConfiguredRepository() {
    // Act
    JpaCommonRepository<DatabaseConnectionEntity, Long> repository = databaseConnectionService.getRepository();

    // Assert
    assertNotNull(repository);
    assertSame(databaseConnectionRepository, repository, "The returned repository should be the mocked instance.");
  }

  @Test
  void getRepository_consistency_returnsSameInstance() {
    // Act
    JpaCommonRepository<DatabaseConnectionEntity, Long> repository1 = databaseConnectionService.getRepository();
    JpaCommonRepository<DatabaseConnectionEntity, Long> repository2 = databaseConnectionService.getRepository();

    // Assert
    assertNotNull(repository1);
    assertNotNull(repository2);
    assertSame(repository1, repository2, "Multiple calls should return the same repository instance.");
    assertEquals(databaseConnectionRepository, repository1);
    assertEquals(databaseConnectionRepository, repository2);
  }

  @Test
  void serviceImplementation_extendsBaseServiceImpl() {
    // Assert
    assertNotNull(databaseConnectionService);
    // Verify that the service is properly instantiated and extends BaseServiceImpl
    // This test ensures the service follows the expected inheritance pattern
  }

  @Test
  void repositoryInjection_success() {
    // Assert
    assertNotNull(databaseConnectionService.getRepository());
    // Verify that dependency injection works correctly
    assertEquals(databaseConnectionRepository, databaseConnectionService.getRepository());
  }
}

