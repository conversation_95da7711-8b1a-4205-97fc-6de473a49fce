package vn.com.mbbank.kanban.mbmonitor.external.execution.controllers;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionService;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ExecutionController.
 */
@ExtendWith(MockitoExtension.class)
class ExecutionControllerTest {

  @Mock
  private ExecutionService executionService;

  @InjectMocks
  private ExecutionController executionController;

  /**
   * Creates a test request for use in tests.
   */
  @Test
  void execute_success() throws BusinessException {
    ExecutionScriptRequest request = new ExecutionScriptRequest();
    request.setExecutionId("Test Script");
    request.setExecutionBy("Test User");

    List<ExecuteScriptParamModel> params = new ArrayList<>();
    ExecuteScriptParamModel param = new ExecuteScriptParamModel();
    param.setName("TEST_PARAM");
    param.setValue("test_value");
    params.add(param);

    request.setParams(params);

    var res = when(executionService.execute(any())).thenReturn(new ExecutionScriptResponse());
    executionController.execute(request);
    assertNotNull(res);
  }

  /**
   * Creates a test request for use in tests.
   */
  @Test
  void execute_failed() throws BusinessException {
    ExecutionScriptRequest request = new ExecutionScriptRequest();
    request.setExecutionId("Test Script");
    request.setExecutionBy("Test User");

    List<ExecuteScriptParamModel> params = new ArrayList<>();
    ExecuteScriptParamModel param = new ExecuteScriptParamModel();
    param.setName("TEST_PARAM");
    param.setValue("test_value");
    params.add(param);

    request.setParams(params);

    when(executionService.execute(any())).thenThrow(new BusinessException(ErrorCode.EXECUTION_PARAM_IS_INVALID));
    assertThrows(BusinessException.class, () -> executionController.execute(request));
  }
}