package vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.impl;

import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.VariableRepositoryCustom;

/**
 * variableRepositoryCustomImpl.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class VariableRepositoryCustomImpl implements VariableRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public List<String> getReferencedDynamicExecutionIds(String executionId) {
    var query = new PrepareQuery("""
          SELECT DISTINCT v.EXECUTION_ID
          FROM VARIABLE v
          JOIN EXECUTION_PARAM p ON v.NAME = p.NAME
          WHERE p.EXECUTION_ID = :executionId
            AND v.TYPE = 'DYNAMIC_VALUE'
        """, "executionId", executionId);
    
    return sqlQueryUtil.queryModel()
      .queryForList(query.getQuery(), query.getParams(), String.class);
  }
}
