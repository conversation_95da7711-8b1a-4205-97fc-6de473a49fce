package vn.com.mbbank.kanban.mbmonitor.common.dtos.models;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.constants.KanbanRegexContants;

/**
 * Model request of action config.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class VariableDetailModel {
  String id;
  @NotBlank
  @Size(max = CommonConstants.COMMON_QUERY_SQL_NAME_MAX_LENGTH)
  @Size(min = 1)
  String name;
  
  @Pattern(
      regexp = KanbanRegexContants.JSON_PATH,
      message = "Position of json invalid mapping json format"
  )
  @Size(max = CommonConstants.MAX_JSON_PATH_LENGTH, message = "Position of json exceeds "
      + CommonConstants.MAX_JSON_PATH_LENGTH + " characters")
  @NotNull
  String jsonPath;
  boolean hidden;
}
