package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecutionKeyValue;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionApiResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionApiEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionApiAuthTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionApiMethodEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionBodyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.ExecutionApiRepository;

@ExtendWith(MockitoExtension.class)
class ExecutionApiServiceImplTest {

  @Mock
  private ExecutionApiRepository executionApiRepository;

  @Mock
  private RestTemplate restTemplateSkipSsl;

  @Mock
  private RestTemplate restTemplateWithSsl;

  @InjectMocks
  private ExecutionApiServiceImpl executionApiService;

  @BeforeEach
  void setUp() {
    // Inject the mocked RestTemplate instances into the service
    ReflectionTestUtils.setField(executionApiService, "restTemplateSkipSsl", restTemplateSkipSsl);
    ReflectionTestUtils.setField(executionApiService, "restTemplateWithSsl", restTemplateWithSsl);
  }

  @Test
  void getRepository_success_returnsConfiguredRepository() {
    // Act
    JpaCommonRepository<ExecutionApiEntity, String> repository = executionApiService.getRepository();

    // Assert
    assertNotNull(repository);
    assertEquals(executionApiRepository, repository);
  }

  @Test
  void findAllByExecutionId_success_returnsExecutionApiEntity() {
    // Arrange
    String executionId = "test-execution-id";
    ExecutionApiEntity expectedEntity = createTestExecutionApiEntity();
    when(executionApiRepository.findAllByExecutionId(executionId)).thenReturn(expectedEntity);

    // Act
    ExecutionApiEntity result = executionApiService.findAllByExecutionId(executionId);

    // Assert
    assertNotNull(result);
    assertEquals(expectedEntity, result);
  }

  @Test
  void callExecutionApi_success_withSslEnabled() {
    // Arrange
    ExecutionApiEntity apiEntity = createTestExecutionApiEntity();
    apiEntity.setEnableSsl(true);
    apiEntity.setUrl("https://api.example.com/test");
    apiEntity.setMethod(ExecutionApiMethodEnum.GET);

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Success response", HttpStatus.OK);
    when(restTemplateWithSsl.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
        .thenReturn(mockResponse);

    // Act
    ExecutionApiResponse result = executionApiService.callExecutionApi(apiEntity);

    // Assert
    assertNotNull(result);
    assertEquals(HttpStatus.OK, result.getStatus());
    assertEquals(200, result.getStatusCode());
    assertEquals("OK", result.getStatusText());
    assertEquals("Success response", result.getBody());
  }


  @Test
  void callExecutionApi_success_withHeaders() {
    // Arrange
    ExecutionApiEntity apiEntity = createTestExecutionApiEntity();
    apiEntity.setEnableSsl(false);
    apiEntity.setUrl("https://api.thecatapi.com/v1/images/search");
    apiEntity.setMethod(ExecutionApiMethodEnum.GET);

    List<ExecutionKeyValue> headers = new ArrayList<>();
    ExecutionKeyValue header1 = new ExecutionKeyValue();
    header1.setKey("Authorization");
    header1.setValue("Bearer token123");
    header1.setEnable(true);
    headers.add(header1);

    ExecutionKeyValue header2 = new ExecutionKeyValue();
    header2.setKey("Content-Type");
    header2.setValue("application/json");
    header2.setEnable(false); // This should be ignored
    headers.add(header2);

    apiEntity.setHeaders(headers);

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Success response", HttpStatus.OK);
    when(restTemplateSkipSsl.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
        .thenReturn(mockResponse);

    // Act
    ExecutionApiResponse result = executionApiService.callExecutionApi(apiEntity);

    // Assert
    assertNotNull(result);
    assertEquals(HttpStatus.OK, result.getStatus());
    assertEquals("Success response", result.getBody());
  }

  @Test
  void callExecutionApi_success_withQueryParams() {
    // Arrange
    ExecutionApiEntity apiEntity = createTestExecutionApiEntity();
    apiEntity.setEnableSsl(false);
    apiEntity.setUrl("https://dog.ceo/api/breeds/image/random");
    apiEntity.setMethod(ExecutionApiMethodEnum.GET);
    
    List<ExecutionKeyValue> params = new ArrayList<>();
    ExecutionKeyValue param1 = new ExecutionKeyValue();
    param1.setKey("page");
    param1.setValue("1");
    param1.setEnable(true);
    params.add(param1);
    
    ExecutionKeyValue param2 = new ExecutionKeyValue();
    param2.setKey("size");
    param2.setValue("10");
    param2.setEnable(false); // This should be ignored
    params.add(param2);
    
    apiEntity.setParams(params);

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Success response", HttpStatus.OK);
    when(restTemplateSkipSsl.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
        .thenReturn(mockResponse);

    // Act
    ExecutionApiResponse result = executionApiService.callExecutionApi(apiEntity);

    // Assert
    assertNotNull(result);
    assertEquals(HttpStatus.OK, result.getStatus());
    assertEquals("Success response", result.getBody());
  }

  @Test
  void callExecutionApi_success_withRawBody() {
    // Arrange
    ExecutionApiEntity apiEntity = createTestExecutionApiEntity();
    apiEntity.setEnableSsl(false);
    apiEntity.setUrl("http://api.example.com/test");
    apiEntity.setMethod(ExecutionApiMethodEnum.POST);
    apiEntity.setBodyType(ExecutionBodyTypeEnum.RAW);
    apiEntity.setBodyRaw("{\"test\": \"data\"}");

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Success response", HttpStatus.OK);
    when(restTemplateSkipSsl.exchange(anyString(), eq(HttpMethod.POST), any(HttpEntity.class), eq(String.class)))
        .thenReturn(mockResponse);

    // Act
    ExecutionApiResponse result = executionApiService.callExecutionApi(apiEntity);

    // Assert
    assertNotNull(result);
    assertEquals(HttpStatus.OK, result.getStatus());
    assertEquals("Success response", result.getBody());
  }

  @Test
  void callExecutionApi_success_withUrlEncodedBody() {
    // Arrange
    ExecutionApiEntity apiEntity = createTestExecutionApiEntity();
    apiEntity.setEnableSsl(false);
    apiEntity.setUrl("https://dog.ceo/api/breeds/image/random");
    apiEntity.setMethod(ExecutionApiMethodEnum.GET);
    apiEntity.setBodyType(ExecutionBodyTypeEnum.URLENCODED);

    List<ExecutionKeyValue> formData = new ArrayList<>();
    ExecutionKeyValue formField1 = new ExecutionKeyValue();
    formField1.setKey("username");
    formField1.setValue("testuser");
    formField1.setEnable(true);
    formData.add(formField1);

    ExecutionKeyValue formField2 = new ExecutionKeyValue();
    formField2.setKey("password");
    formField2.setValue("testpass");
    formField2.setEnable(false); // This should be ignored
    formData.add(formField2);

    apiEntity.setFormUrlEncoded(formData);

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Success response", HttpStatus.OK);
    when(restTemplateSkipSsl.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
        .thenReturn(mockResponse);

    // Act
    ExecutionApiResponse result = executionApiService.callExecutionApi(apiEntity);

    // Assert
    assertNotNull(result);
    assertEquals(HttpStatus.OK, result.getStatus());
    assertEquals("Success response", result.getBody());
  }

  @Test
  void callExecutionApi_success_withUrlContainingQueryString() {
    // Arrange
    ExecutionApiEntity apiEntity = createTestExecutionApiEntity();
    apiEntity.setEnableSsl(false);
    apiEntity.setUrl("https://dog.ceo/api/breeds/image/random");
    apiEntity.setMethod(ExecutionApiMethodEnum.GET);

    List<ExecutionKeyValue> params = new ArrayList<>();
    ExecutionKeyValue param1 = new ExecutionKeyValue();
    param1.setKey("page");
    param1.setValue("1");
    param1.setEnable(true);
    params.add(param1);

    apiEntity.setParams(params);

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Success response", HttpStatus.OK);
    when(restTemplateSkipSsl.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
        .thenReturn(mockResponse);

    // Act
    ExecutionApiResponse result = executionApiService.callExecutionApi(apiEntity);

    // Assert
    assertNotNull(result);
    assertEquals(HttpStatus.OK, result.getStatus());
    assertEquals("Success response", result.getBody());
  }

  @Test
  void callExecutionApi_httpClientErrorException_returnsErrorResponse() {
    // Arrange
    ExecutionApiEntity apiEntity = createTestExecutionApiEntity();
    apiEntity.setEnableSsl(false);
    apiEntity.setUrl("https://dog.ceo/api/breeds/image/random");
    apiEntity.setMethod(ExecutionApiMethodEnum.GET);

    HttpHeaders responseHeaders = new HttpHeaders();
    responseHeaders.setContentType(MediaType.APPLICATION_JSON);

    HttpClientErrorException exception = new HttpClientErrorException(
        HttpStatus.BAD_REQUEST, "Bad Request", responseHeaders, "Error response".getBytes(), null);

    when(restTemplateSkipSsl.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
        .thenThrow(exception);

    // Act
    ExecutionApiResponse result = executionApiService.callExecutionApi(apiEntity);

    // Assert
    assertNotNull(result);
    assertEquals(HttpStatus.BAD_REQUEST, result.getStatus());
    assertEquals(400, result.getStatusCode());
    assertEquals("Bad Request", result.getStatusText());
    assertEquals(MediaType.APPLICATION_JSON.toString(), result.getContentType());
    assertEquals("Error response", result.getBody());
  }

  @Test
  void callExecutionApi_httpClientErrorException_withoutContentType() {
    // Arrange
    ExecutionApiEntity apiEntity = createTestExecutionApiEntity();
    apiEntity.setEnableSsl(false);
    apiEntity.setUrl("https://dog.ceo/api/breeds/image/random");
    apiEntity.setMethod(ExecutionApiMethodEnum.GET);

    HttpClientErrorException exception = new HttpClientErrorException(
        HttpStatus.NOT_FOUND, "Not Found", null, "Not found".getBytes(), null);

    when(restTemplateSkipSsl.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
        .thenThrow(exception);

    // Act
    ExecutionApiResponse result = executionApiService.callExecutionApi(apiEntity);

    // Assert
    assertNotNull(result);
    assertEquals(HttpStatus.NOT_FOUND, result.getStatus());
    assertEquals(404, result.getStatusCode());
    assertEquals("Not Found", result.getStatusText());
    assertEquals(null, result.getContentType());
    assertEquals("Not found", result.getBody());
  }

  @Test
  void callExecutionApi_generalException_returnsInternalServerError() {
    // Arrange
    ExecutionApiEntity apiEntity = createTestExecutionApiEntity();
    apiEntity.setEnableSsl(false);
    apiEntity.setUrl("https://dog.ceo/api/breeds/image/random");
    apiEntity.setMethod(ExecutionApiMethodEnum.GET);

    RuntimeException exception = new RuntimeException("Connection timeout");

    when(restTemplateSkipSsl.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
        .thenThrow(exception);

    // Act
    ExecutionApiResponse result = executionApiService.callExecutionApi(apiEntity);

    // Assert
    assertNotNull(result);
    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, result.getStatus());
    assertEquals(500, result.getStatusCode());
    assertEquals("text/plain", result.getContentType());
    assertEquals("Could not send request: Connection timeout", result.getBody());
  }

  @Test
  void callExecutionApi_success_withEmptyHeaders() {
    // Arrange
    ExecutionApiEntity apiEntity = createTestExecutionApiEntity();
    apiEntity.setEnableSsl(false);
    apiEntity.setUrl("https://dog.ceo/api/breeds/image/random");
    apiEntity.setMethod(ExecutionApiMethodEnum.GET);
    apiEntity.setHeaders(new ArrayList<>()); // Empty headers list

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Success response", HttpStatus.OK);
    when(restTemplateSkipSsl.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
        .thenReturn(mockResponse);

    // Act
    ExecutionApiResponse result = executionApiService.callExecutionApi(apiEntity);

    // Assert
    assertNotNull(result);
    assertEquals(HttpStatus.OK, result.getStatus());
    assertEquals("Success response", result.getBody());
  }

  @Test
  void callExecutionApi_success_withEmptyParams() {
    // Arrange
    ExecutionApiEntity apiEntity = createTestExecutionApiEntity();
    apiEntity.setEnableSsl(false);
    apiEntity.setUrl("https://dog.ceo/api/breeds/image/random");
    apiEntity.setMethod(ExecutionApiMethodEnum.GET);
    apiEntity.setParams(new ArrayList<>()); // Empty params list

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Success response", HttpStatus.OK);
    when(restTemplateSkipSsl.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
        .thenReturn(mockResponse);

    // Act
    ExecutionApiResponse result = executionApiService.callExecutionApi(apiEntity);

    // Assert
    assertNotNull(result);
    assertEquals(HttpStatus.OK, result.getStatus());
    assertEquals("Success response", result.getBody());
  }

  /**
   * Creates a test ExecutionApiEntity for use in tests.
   */
  private ExecutionApiEntity createTestExecutionApiEntity() {
    ExecutionApiEntity entity = new ExecutionApiEntity();
    entity.setId("test-api-id");
    entity.setExecutionId("test-execution-id");
    entity.setUrl("https://dog.ceo/api/breeds/image/random");
    entity.setMethod(ExecutionApiMethodEnum.GET);
    entity.setEnableSsl(false);
    entity.setBodyType(ExecutionBodyTypeEnum.NONE);
    entity.setAuthType(ExecutionApiAuthTypeEnum.NONE);
    return entity;
  }

  private ExecutionApiEntity mockApiEntity() {
    ExecutionApiEntity entity = new ExecutionApiEntity();
    entity.setUrl("https://jsonplaceholder.typicode.com/posts");
    entity.setMethod(ExecutionApiMethodEnum.POST);
    entity.setBodyType(ExecutionBodyTypeEnum.RAW);
    entity.setBodyRaw("{\"title\":\"test\"}");
    entity.setEnableSsl(false);
    entity.setAuthType(ExecutionApiAuthTypeEnum.NONE);
    return entity;
  }

  @Test
  void testCallExecutionApi_Success() {
    ExecutionApiEntity apiInfo = mockApiEntity();

    ResponseEntity<String> responseEntity = new ResponseEntity<>(
            "{\"id\": 101}", HttpStatus.CREATED);

    when(restTemplateSkipSsl.exchange(
            eq(apiInfo.getUrl()),
            eq(HttpMethod.POST),
            any(HttpEntity.class),
            eq(String.class)
    )).thenReturn(responseEntity);

    ExecutionApiResponse response = executionApiService.callExecutionApi(apiInfo);

    assertEquals(201, response.getStatusCode());
    assertEquals(HttpStatus.CREATED, response.getStatus());
    assertNotNull(response.getBody());
    assertTrue(response.getBody().contains("101"));
  }

  @Test
  void testCallExecutionApi_HttpClientErrorException() {
    ExecutionApiEntity apiInfo = mockApiEntity();

    HttpClientErrorException exception = new HttpClientErrorException(
            HttpStatus.BAD_REQUEST, "Bad Request", "Invalid".getBytes(), null);

    when(restTemplateSkipSsl.exchange(
            anyString(), any(), any(), eq(String.class)))
            .thenThrow(exception);

    ExecutionApiResponse response = executionApiService.callExecutionApi(apiInfo);

    assertEquals(400, response.getStatusCode());
    assertEquals(HttpStatus.BAD_REQUEST, response.getStatus());
    assertTrue(response.getBody().contains("Invalid"));
  }

  @Test
  void testCallExecutionApi_GeneralException() {
    ExecutionApiEntity apiInfo = mockApiEntity();

    when(restTemplateSkipSsl.exchange(
            anyString(), any(), any(), eq(String.class)))
            .thenThrow(new RuntimeException("Timeout"));

    ExecutionApiResponse response = executionApiService.callExecutionApi(apiInfo);

    assertEquals(500, response.getStatusCode());
    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatus());
    assertTrue(response.getBody().contains("Timeout"));
  }
}
