package vn.com.mbbank.kanban.mbmonitor.external.execution.mapper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionHistoryEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionStatusEnum;

/**
 * ExecutionHistoryEntityMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ExecutionHistoryEntityMapper extends KanbanBaseMapper<ExecutionHistoryEntity, ExecutionScriptRequest> {
  ExecutionHistoryEntityMapper INSTANCE = Mappers.getMapper(ExecutionHistoryEntityMapper.class);

  /**
   * map request to entity.
   *
   * @param request   ExecuteScriptRequest
   * @param execution ExecutionEntity
   * @param params    list of ExecuteScriptParamModel
   * @return ExecutionHistoryEntity
   */
  default ExecutionHistoryEntity map(ExecutionScriptRequest request, ExecutionEntity execution,
                                     List<ExecuteScriptParamModel> params) {
    if (request == null) {
      return null;
    }

    ExecutionHistoryEntity executionHistoryEntity = new ExecutionHistoryEntity();

    executionHistoryEntity.setExecutionName(execution.getName());
    executionHistoryEntity.setExecutionDescription(execution.getDescription());
    executionHistoryEntity.setExecutionBy(request.getExecutionBy());
    executionHistoryEntity.setExecutionScript(execution.getScript());
    executionHistoryEntity.setExecutionType(execution.getType());
    if (params != null) {
      executionHistoryEntity.setExecutionParams(new ArrayList<>(params));
    }
    executionHistoryEntity.setStatus(ExecutionStatusEnum.IN_PROGRESS);
    executionHistoryEntity.setStartTime(new Date());
    return executionHistoryEntity;
  }
}
