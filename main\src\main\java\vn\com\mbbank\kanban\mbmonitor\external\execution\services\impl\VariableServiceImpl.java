package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.VariableRepository;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.VariableService;

@Service
@RequiredArgsConstructor
public class VariableServiceImpl extends BaseServiceImpl<VariableEntity, String>
    implements VariableService {
  private final VariableRepository variableRepository;

  @Override
  protected JpaCommonRepository<VariableEntity, String> getRepository() {
    return variableRepository;
  }

  @Override
  public VariableEntity findWithId(String id) throws BusinessException {
    return variableRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.VARIABLE_NOT_FOUND));
  }
  
  @Override
  public List<VariableEntity> findAllByNameIn(List<String> names) {
    return variableRepository.findAllByNameIn(names);
  }
  
  @Override
  public List<String> getReferencedDynamicExecutionIds(String executionId) {
    return variableRepository.getReferencedDynamicExecutionIds(executionId);
  }
  
  @Override
  public List<VariableEntity> findAllByExecutionId(String executionId) {
    return variableRepository.findAllByExecutionId(executionId);
  }
}
