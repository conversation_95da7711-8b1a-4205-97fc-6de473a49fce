package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.VariableDataTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.VariableTypeEnum;

@Entity
@Data
@Table(name = TableName.VARIABLE)
@EqualsAndHashCode(callSuper = true)
@KanbanAutoGenerateUlId
public class VariableEntity extends BaseEntity<String> {
  
  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "NAME")
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "VALUE")
  private String value;

  @Column(name = "HIDDEN")
  private boolean hidden;
  
  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private VariableTypeEnum type;
  
  @Column(name = "EXECUTION_ID")
  private String executionId;
  
  @Column(name = "DATA_TYPE")
  @Enumerated(EnumType.STRING)
  private VariableDataTypeEnum dataType;
  
  @Column(name = "JSON_PATH")
  private String jsonPath;
  
  @Column(name = "EXPIRATION_TIME")
  private Long expirationTime;
  
  @Column(name = "ENABLE_EXPIRATION")
  private boolean enableExpiration;

  @Override
  public String getId() {
    return id;
  }
}
