package vn.com.mbbank.kanban.mbmonitor.external.execution.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;

/**
 * Repository table Variable.
 */
@Repository
public interface VariableRepository
    extends JpaCommonRepository<VariableEntity, String>, VariableRepositoryCustom {

  /**
   * find variable by names.
   *
   * @param names variable variable name
   * @return list of variable
   */
  List<VariableEntity> findAllByNameIn(List<String> names);
  
  /**
   * find variable by executionId.
   *
   * @param executionId executionId
   * @return list of variable
   */
  List<VariableEntity> findAllByExecutionId(String executionId);
}
