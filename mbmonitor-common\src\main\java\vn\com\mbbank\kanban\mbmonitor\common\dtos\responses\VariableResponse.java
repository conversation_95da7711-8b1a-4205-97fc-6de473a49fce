package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.VariableDataTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.VariableTypeEnum;

/**
 * VariableResponse.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class VariableResponse {
  String id;
  String name;
  String description;
  String value;
  boolean hidden;
  VariableTypeEnum type;
  String executionId;
  String executionName;
  VariableDataTypeEnum dataType;
  String jsonPath;
  Long expirationTime;
  boolean enableExpiration;
}
