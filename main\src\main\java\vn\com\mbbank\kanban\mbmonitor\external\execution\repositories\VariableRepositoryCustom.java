package vn.com.mbbank.kanban.mbmonitor.external.execution.repositories;

import java.util.List;

/**
 * VariableRepositoryCustom.
 */
public interface VariableRepositoryCustom {
  /**
   * Returns execution IDs of variables with type 'DYNAMIC_VALUE'
   * that are referenced by parameters in the given execution.
   *
   * @param executionId the execution ID to check
   * @return list of referenced execution IDs
   */
  List<String> getReferencedDynamicExecutionIds(String executionId);
}
