package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.VariableDetailModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.VariableDataTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.VariableTypeEnum;

/**
 * VariableRequest.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class VariableRequest {
  String id;
  String name;
  @Size(max = CommonConstants.COMMON_DESCRIPTION_MAX_LENGTH)
  String description;
  @Size(max = CommonConstants.VARIABLE_MAX_LENGTH)
  String value;
  boolean hidden;
  @NotNull
  VariableTypeEnum type;
  String executionId;
  VariableDataTypeEnum dataType;
  List<VariableDetailModel> variableDetails;
  boolean enableExpiration;
  Long expirationTime;
}
