package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.JsonPath;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.annotation.Nullable;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.mbmonitor.common.configs.QueryHikariDataSourceConfig;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.VariableRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionApiEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionHistoryEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionParamEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.VariableDataTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.VariableTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.DatabaseConnectionRequestToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.DatabaseQueryService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DatabaseCollectUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ExecutionApiParamUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.SqlUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.VariableUtils;
import vn.com.mbbank.kanban.mbmonitor.external.execution.mapper.ExecutionHistoryEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.ExecutionRepository;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.DatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionApiService;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionHistoryService;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionParamService;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionService;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.VariableService;

@Service
@RequiredArgsConstructor
@Slf4j
public class ExecutionServiceImpl implements ExecutionService {
  private static final int BUFFER_SIZE = 8192;
  private final Logger logger = LoggerFactory.getLogger(this.getClass());
  private final ExecutionHistoryService executionHistoryService;
  private final ExecutionRepository executionRepository;
  private final DatabaseConnectionService databaseConnectionService;
  private final QueryHikariDataSourceConfig queryHikariDataSourceConfig;
  private final DatabaseQueryService databaseQueryService;
  private final ExecutionApiService executionApiService;
  private final ExecutionParamService executionParamService;
  private final VariableService variableService;
  private final ObjectMapper objectMapper;
  private final ExecutionHistoryEntityMapper executionHistoryEntityMapper = ExecutionHistoryEntityMapper.INSTANCE;
  @Value("${mbmonitor.execution.maxOutputSize:524288}")
  private int maxOutputSize;
  @Value("${mbmonitor.execution.timeout:5}")
  private int executeTimeOut;

  @Override
  public ExecutionScriptResponse execute(ExecutionScriptRequest request) throws BusinessException {
    validateRequest(request);
    return runExecution(request);
  }

  protected void validateRequest(ExecutionScriptRequest request) throws BusinessException {
    var execution = executionRepository.findById(request.getExecutionId())
        .orElseThrow(() -> new BusinessException(ErrorCode.EXECUTION_NOT_FOUND));
    if (ExecutionTypeEnum.SQL.equals(execution.getType())) {
      var databaseConnection = databaseConnectionService.findById(execution.getDatabaseConnectionId());
      if (Objects.isNull(databaseConnection)) {
        throw new BusinessException(ErrorCode.DATABASE_CONNECTION_NOT_FOUND);
      }
      if (!databaseConnection.getIsActive()) {
        throw new BusinessException(ErrorCode.DATABASE_COLLECT_CONNECTION_INACTIVE);
      }
    }
    if (ExecutionTypeEnum.API.equals(execution.getType())) {
      var executionApiEntity = executionApiService.findAllByExecutionId(execution.getId());
      if (KanbanCommonUtil.isEmpty(executionApiEntity)) {
        throw new BusinessException(ErrorCode.EXECUTION_API_INFO_INVALID);
      }
    }
    validateRecursiveExecutionDependency(request.getExecutionId());
  }


  protected ExecutionScriptResponse runExecution(ExecutionScriptRequest request) throws BusinessException {
    var execution = executionRepository.getReferenceById(request.getExecutionId());
    logger.info("Starting run execution : {} executionBy : {} ", request.getExecutionId(), request.getExecutionBy());
    var params = buildParams(request.getVariables(), execution);
    // save start execution history
    var startHistory = canStoreHistory(execution)
            ? executionHistoryService.save(executionHistoryEntityMapper.map(request, execution, params))
            : null;
    var response = switch (execution.getType()) {
      case API -> callApi(request, params);
      case SQL -> runSqlScript(request, params);
      case PYTHON -> runPythonScript(request, params);
    };
    saveCompleteExecutionHistory(startHistory, request, response);
    logger.info("Completed run execution : {} executionBy : {} ", request.getExecutionId(), request.getExecutionBy());
    return response;
  }

  protected boolean canStoreHistory(ExecutionEntity execution) {
    return !ExecutionTypeEnum.SQL.equals(execution.getType());
  }

  protected void saveCompleteExecutionHistory(@Nullable ExecutionHistoryEntity executionHistory,
                                              ExecutionScriptRequest request,
                                              ExecutionScriptResponse response) {
    if (Objects.nonNull(executionHistory)) {
      var execution = executionRepository.getReferenceById(request.getExecutionId());
      if (canStoreHistory(execution)) {
        executionHistory.setResult(response.getResult());
        executionHistory.setEndTime(new Date());
        executionHistory.setStatus(response.getStatus());
        executionHistory.setError(response.getError());
        executionHistory.setExecutionBy(request.getExecutionBy());
        executionHistoryService.save(executionHistory);
      }
    }
  }

  protected ExecutionScriptResponse runPythonScript(ExecutionScriptRequest request,
                                                    List<ExecuteScriptParamModel> params) {
    var execution = executionRepository.getReferenceById(request.getExecutionId());
    var res = new ExecutionScriptResponse();
    File pythonFile = null;
    var script = execution.getScript();

    try {
      // Create temp python file
      pythonFile = File.createTempFile("python_script_", ".py");

      // Write content to file
      try (FileWriter writer = new FileWriter(pythonFile)) {
        writer.write(script);
      }

      // Create ProcessBuilder to run Python
      ProcessBuilder processBuilder = new ProcessBuilder("python", "-X", "utf8", pythonFile.getAbsolutePath());

      // Add ENV
      if (!params.isEmpty()) {
        Map<String, String> env = processBuilder.environment();
        env.putAll(
            params.stream()
                .collect(Collectors.toMap(ExecuteScriptParamModel::getName, ExecuteScriptParamModel::getValue)));
      }

      // Run process
      Process process = processBuilder.start();

      // Use CompletableFuture for async output reading with limits
      CompletableFuture<String> outputFuture = CompletableFuture.supplyAsync(() ->
          readStreamWithLimit(process.getInputStream(), "stdout"));

      CompletableFuture<String> errorFuture = CompletableFuture.supplyAsync(() ->
          readStreamWithLimit(process.getErrorStream(), "stderr"));

      // Wait for process with timeout
      boolean exited = process.waitFor(executeTimeOut, TimeUnit.MINUTES);

      if (!exited) {
        process.destroyForcibly();
        res.setStatus(ExecutionStatusEnum.FAILED);
      } else {
        int exitCode = process.exitValue();
        if (exitCode == 0) {
          res.setStatus(ExecutionStatusEnum.COMPLETED);
        } else {
          res.setStatus(ExecutionStatusEnum.FAILED);
        }
      }

      // Get outputs (with timeout to avoid hanging)
      try {
        String output = outputFuture.get(executeTimeOut, TimeUnit.MINUTES);
        String error =
            errorFuture.get(executeTimeOut, TimeUnit.MINUTES)
                + (exited ? "" : "\nPython script execution timed out");
        res.setResult(output);
        res.setError(error);
      } catch (TimeoutException e) {
        outputFuture.cancel(true);
        errorFuture.cancel(true);
        res.setError(res.getError() + "\nOutput reading timed out - output may be truncated");
      }
    } catch (Exception e) {
      logger.error(e.getMessage(), e);
      res.setStatus(ExecutionStatusEnum.FAILED);
      res.setError("Error executing Python script: " + e.getMessage());
    } finally {
      if (Objects.nonNull(pythonFile)) {
        try {
          pythonFile.delete();
        } catch (Exception ex) {
          logger.error("Can not delete temp execution file ", ex);
        }
      }
    }
    return res;
  }

  protected ExecutionScriptResponse callApi(ExecutionScriptRequest request, List<ExecuteScriptParamModel> params) {
    var executionApiInfo = executionApiService.findAllByExecutionId(request.getExecutionId());
    var mapApiInfo = objectMapper.convertValue(executionApiInfo, ExecutionApiEntity.class);
    if (!KanbanCommonUtil.isEmpty(params)) {
      ExecutionApiParamUtils.fillTemplateInObject(mapApiInfo, params);
    }
    var executionApiResponse = executionApiService.callExecutionApi(mapApiInfo);
    var isSuccess = !KanbanCommonUtil.isEmpty(executionApiResponse.getStatus())
            && executionApiResponse.getStatus().is2xxSuccessful();
    var executionScriptResponse = new ExecutionScriptResponse();
    executionScriptResponse.setApiExecutionResponse(executionApiResponse);
    if (isSuccess) {
      executionScriptResponse.setStatus(ExecutionStatusEnum.COMPLETED);
      executionScriptResponse.setResult(executionApiResponse.getBody());
    } else {
      executionScriptResponse.setStatus(ExecutionStatusEnum.FAILED);
      executionScriptResponse.setError(executionApiResponse.getStatus() + "\n" + executionApiResponse.getBody());
    }
    return executionScriptResponse;
  }

  protected void validateExecutionRunningRequest(List<ExecuteScriptParamModel> params) throws BusinessException {
    var isInvalidParams = params.stream()
            .anyMatch(param -> Objects.isNull(param.getValue()) || KanbanCommonUtil.isEmpty(param.getName()));
    if (isInvalidParams) {
      throw new BusinessException(ErrorCode.EXECUTION_PARAM_IS_INVALID);
    }
  }


  // Helper method to read stream with size limit
  protected String readStreamWithLimit(InputStream inputStream, String streamName) {
    StringBuilder output = new StringBuilder();
    byte[] buffer = new byte[BUFFER_SIZE];
    int totalBytes = 0;
    boolean truncated = false;

    try (BufferedInputStream bis = new BufferedInputStream(inputStream)) {
      int bytesRead;
      while ((bytesRead = bis.read(buffer)) != -1) {
        if (totalBytes + bytesRead > maxOutputSize) {
          // Calculate how many bytes we can still read
          int remainingBytes = maxOutputSize - totalBytes;
          if (remainingBytes > 0) {
            output.append(new String(buffer, 0, remainingBytes, StandardCharsets.UTF_8));
          }
          truncated = true;
          break;
        }
        output.append(new String(buffer, 0, bytesRead, StandardCharsets.UTF_8));
        totalBytes += bytesRead;
      }
    } catch (IOException e) {
      logger.error("Error reading " + streamName, e);
      output.append("\nError reading ").append(streamName).append(": ").append(e.getMessage());
    }

    if (truncated) {
      output.append("\n[OUTPUT TRUNCATED - Exceeded ").append(maxOutputSize).append(" bytes limit]");
    }

    return output.toString();
  }

  protected ExecutionScriptResponse runSqlScript(ExecutionScriptRequest request, List<ExecuteScriptParamModel> params) {
    var execution = executionRepository.getReferenceById(request.getExecutionId());
    var databaseConnection = databaseConnectionService.findById(execution.getDatabaseConnectionId());
    DatabaseConnectionRequest databaseConnectionRequest =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.map(databaseConnection);
    databaseConnectionRequest.setPassword(KanbanEncryptorUtils.decrypt(databaseConnectionRequest.getPassword()));
    HikariConfig dbConfig = SqlUtils.createHikariConfig(databaseConnectionRequest);
    var response = new ExecutionScriptResponse();
    SqlExecutionResponse sqlResponse = null;
    try (HikariDataSource dataSource = queryHikariDataSourceConfig.getDataSource(dbConfig.getJdbcUrl())) {
      var script = execution.getScript();
      Map<String, Object> paramMap =
              params.stream()
              .collect(Collectors.toMap(ExecuteScriptParamModel::getName, ExecuteScriptParamModel::getValue));
      sqlResponse = databaseQueryService.executeQuery(dataSource.getConnection(),
          DatabaseCollectUtils.getQueryWithPaging(script,
              isSelectQuery(script) ? request.getPaginationRequest() : null), true,
          null, paramMap);
      if (!sqlResponse.isNonQuery()) {
        var totalResponse =
            databaseQueryService.executeQuery(dataSource.getConnection(),
                DatabaseCollectUtils.getQueryWithCount(script),
                true, null, paramMap);
        sqlResponse.setTotal(Integer.valueOf(
            totalResponse.getListDataMappings().get(0).getListSqlMappingColumnDatas().get(0).getValue()));
      }
      response.setSqlExecutionResponse(sqlResponse);
      response.setStatus(ExecutionStatusEnum.COMPLETED);
    } catch (Exception ex) {
      logger.error(ex.getMessage(), ex);
      response.setStatus(ExecutionStatusEnum.FAILED);
      response.setError(ex.getMessage());
    }
    return response;
  }

  protected boolean isSelectQuery(String sql) {
    try {
      Statement statement = CCJSqlParserUtil.parse(sql);
      return statement instanceof Select;
    } catch (JSQLParserException e) {
      logger.error(e.getMessage(), e);
      return false;
    }
  }

  protected List<ExecuteScriptParamModel> buildParams(List<VariableRequest> requestVariables,
                                                      ExecutionEntity execution) throws BusinessException {
    var requireParams = executionParamService.findAllByExecutionId(execution.getId());
    if (KanbanCommonUtil.isEmpty(requireParams)) {
      return new ArrayList<>();
    }
    var res = new ArrayList<ExecuteScriptParamModel>();
    requestVariables = !KanbanCommonUtil.isEmpty(requestVariables) ? requestVariables : Collections.emptyList();

    var requestVariableMap =
            requestVariables.stream().collect(Collectors.toMap(VariableRequest::getName, Function.identity()));
    var defaultVariableNames =
            requireParams.stream().map(ExecutionParamEntity::getName).filter(Objects::nonNull).toList();
    Map<String, VariableEntity> defaultVariableMap = ExecutionTypeEnum.PYTHON.equals(execution.getType())
            ? variableService.findAllByNameIn(defaultVariableNames).stream()
            .collect(Collectors.toMap(VariableEntity::getName, Function.identity()))
            : Map.of();
    for (ExecutionParamEntity param : requireParams) {
      var name = param.getName();
      var variable = new ExecuteScriptParamModel();
      variable.setName(name);
      if (defaultVariableMap.containsKey(name)) {
        var item = defaultVariableMap.get(name);
        variable.setHidden(item.isHidden());
        variable.setValue(getVariableValueByEntity(item));
      } else {
        variable.setValue(requestVariableMap.containsKey(name)
                ? requestVariableMap.get(name).getValue()
                : null);
      }
      res.add(variable);
    }
    return res;
  }
  
  /**
   * Returns the value of the given variable.
   * If it's a dynamic variable and expired, refreshes its value via execution.
   *
   * @param variableEntity the variable to evaluate
   * @return the current or refreshed value
   * @throws BusinessException if refresh execution fails
   */
  protected String getVariableValueByEntity(VariableEntity variableEntity) throws BusinessException {
    // Handle dynamic value type
    if (VariableTypeEnum.DYNAMIC_VALUE.equals(variableEntity.getType())) {
      // If expiration is disabled or missing, always refresh
      if (!variableEntity.isEnableExpiration() || KanbanCommonUtil.isEmpty(variableEntity.getExpirationTime())) {
        return refreshDynamicValue(variableEntity);
      }
      
      // Check if the value has expired
      Date lastUpdatedAt =  variableEntity.getModifiedDate();
      Long expirationSeconds = variableEntity.getExpirationTime();
      boolean missingRequiredData = KanbanCommonUtil.isEmpty(variableEntity.getValue())
          || KanbanCommonUtil.isEmpty(lastUpdatedAt) || KanbanCommonUtil.isEmpty(expirationSeconds);
      boolean expired = missingRequiredData
          || Instant.now().isAfter(lastUpdatedAt.toInstant().plusSeconds(expirationSeconds));
      // If expired → trigger execution to refresh the value
      if (expired) {
        return refreshDynamicValue(variableEntity);
      }
      // Still valid → return current value
      return VariableUtils.getVariableValueByEntity(variableEntity);
    }
    
    return VariableUtils.getVariableValueByEntity(variableEntity);
  }
  
  /**
   * Validates whether the given executionId causes any recursive reference via dynamic value variables.
   * This method traverses execution dependencies by following dynamic-value variables whose names
   * are referenced in execution parameters. It checks up to MAX_DEPTH levels to detect circular references.
   *
   * @param rootExecutionId the execution ID to start checking from
   * @throws BusinessException if a recursive reference is found or maximum depth is exceeded
   */
  private void validateRecursiveExecutionDependency(String rootExecutionId) throws BusinessException {
    Set<String> visited = new HashSet<>(); // Tracks visited execution IDs to detect cycles
    Queue<String> queue = new LinkedList<>();
    queue.add(rootExecutionId);
    int depth = 0;
    
    while (!queue.isEmpty() && depth <= CommonConstants.MAX_DEPTH_VARIABLE) {
      int size = queue.size();
      for (int i = 0; i < size; i++) {
        String currentExecutionId = queue.poll();
        // If already visited, a recursive loop is detected
        if (!visited.add(currentExecutionId)) {
          throw new BusinessException(ErrorCode.VARIABLE_RECURSIVE_EXECUTION_REFERENCE);
        }
        // Get all executionIds referenced through dynamic-value variables from this execution
        List<String> referencedExecutionIds = variableService.getReferencedDynamicExecutionIds(currentExecutionId);
        queue.addAll(referencedExecutionIds);
      }
      depth++;
    }
    // If more levels remain after max depth, treat it as a potential infinite loop
    if (!queue.isEmpty()) {
      throw new BusinessException(ErrorCode.VARIABLE_RECURSION_DEPTH_EXCEEDED);
    }
  }
  
  /**
   * Refreshes the value of a dynamic variable by re-running its associated execution.
   * Supports both RAW and JSON data types.
   *
   * @param variableEntity the variable to refresh
   * @return the new value extracted from execution
   * @throws BusinessException if execution fails
   */
  private String refreshDynamicValue(VariableEntity variableEntity) throws BusinessException {
    String executionId = variableEntity.getExecutionId();
    // Prepare request to run execution
    ExecutionScriptRequest request = new ExecutionScriptRequest();
    request.setExecutionId(executionId);
    request.setExecutionBy("Kanban_dynamic_value");
    ExecutionScriptResponse executeScriptResponse = runExecution(request);
    
    // Get execution result
    String executionResult = executeScriptResponse.getResult();
    String extractedValue = null;
    
    // If the data type is RAW, use the result directly
    if (VariableDataTypeEnum.RAW.equals(variableEntity.getDataType())) {
      extractedValue = executionResult;
    } else {
      // If data type is JSON, extract value using JSON path
      if (!KanbanCommonUtil.isEmpty(variableEntity.getJsonPath())) {
        extractedValue = getValueByJsonPath(executionResult, variableEntity.getJsonPath());
        
        // Update values for other variables with the same executionId
        List<VariableEntity> variableEntities = variableService.findAllByExecutionId(executionId);
        variableEntities.remove(variableEntity); // exclude the current variable
        if (!KanbanCommonUtil.isEmpty(variableEntities)) {
          for (VariableEntity item : variableEntities) {
            if (!KanbanCommonUtil.isEmpty(item.getJsonPath())) {
              item.setValue(VariableUtils.normalizeVariableValue(
                  item.isHidden(), getValueByJsonPath(executionResult, item.getJsonPath())));
            } else {
              item.setValue(null);
            }
          }
          try {
            variableService.saveAll(variableEntities);
          } catch (Exception e) {
            log.warn("Failed to save related variable entities: {}", e.getMessage(), e);
          }
        }
      }
    }
    
    // Save and return updated value for the current variable
    try {
      variableEntity.setValue(VariableUtils.normalizeVariableValue(variableEntity.isHidden(), extractedValue));
      variableService.save(variableEntity);
    } catch (Exception e) {
      log.warn("Failed to save the current variable entity: {}", e.getMessage(), e);
    }
    return extractedValue;
  }
  
  /**
   * Extracts a value from a JSON string using the provided JsonPath expression.
   *
   * @param body     The JSON string to parse.
   * @param jsonPath The JsonPath expression used to extract the value.
   * @return The extracted value as a string, or an empty string if extraction fails.
   */
  private String getValueByJsonPath(String body, String jsonPath) {
    String result = "";
    try {
      Object value = JsonPath.read(body, jsonPath);
      result = !KanbanCommonUtil.isEmpty(value) ? value.toString() : "";
    } catch (Exception e) {
      log.error("Failed to extract value using JsonPath [{}] from body: {}. Error: {}",
          jsonPath, body, e.getMessage(), e);
    }
    return result;
  }
}