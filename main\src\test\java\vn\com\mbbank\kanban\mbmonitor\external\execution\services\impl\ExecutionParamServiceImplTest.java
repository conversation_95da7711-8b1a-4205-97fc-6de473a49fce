package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionParamEntity;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.ExecutionParamRepository;

@ExtendWith(MockitoExtension.class)
class ExecutionParamServiceImplTest {

  @Mock
  private ExecutionParamRepository executionParamRepository;

  @InjectMocks
  private ExecutionParamServiceImpl executionParamService;

  @Test
  void getRepository_success_returnsConfiguredRepository() {
    // Act
    JpaCommonRepository<ExecutionParamEntity, String> repository = executionParamService.getRepository();

    // Assert
    assertNotNull(repository);
    assertSame(executionParamRepository, repository, "The returned repository should be the mocked instance.");
  }

  @Test
  void findAllByExecutionId_success_returnsListOfParams() {
    // Arrange
    String executionId = "test-execution-id";
    List<ExecutionParamEntity> expectedParams = Arrays.asList(
        createTestExecutionParamEntity("param1", executionId),
        createTestExecutionParamEntity("param2", executionId),
        createTestExecutionParamEntity("param3", executionId)
    );
    
    when(executionParamRepository.findAllByExecutionId(executionId)).thenReturn(expectedParams);

    // Act
    List<ExecutionParamEntity> result = executionParamService.findAllByExecutionId(executionId);

    // Assert
    assertNotNull(result);
    assertEquals(3, result.size());
    assertEquals(expectedParams, result);
    
    // Verify each param has correct execution ID
    for (ExecutionParamEntity param : result) {
      assertEquals(executionId, param.getExecutionId());
    }
  }

  @Test
  void findAllByExecutionId_success_returnsEmptyList() {
    // Arrange
    String executionId = "non-existent-execution-id";
    List<ExecutionParamEntity> emptyList = new ArrayList<>();
    
    when(executionParamRepository.findAllByExecutionId(executionId)).thenReturn(emptyList);

    // Act
    List<ExecutionParamEntity> result = executionParamService.findAllByExecutionId(executionId);

    // Assert
    assertNotNull(result);
    assertTrue(result.isEmpty());
    assertEquals(0, result.size());
  }

  @Test
  void findAllByExecutionId_nullExecutionId_returnsEmptyList() {
    // Arrange
    String executionId = null;
    List<ExecutionParamEntity> emptyList = new ArrayList<>();
    
    when(executionParamRepository.findAllByExecutionId(executionId)).thenReturn(emptyList);

    // Act
    List<ExecutionParamEntity> result = executionParamService.findAllByExecutionId(executionId);

    // Assert
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void findAllByExecutionId_emptyExecutionId_returnsEmptyList() {
    // Arrange
    String executionId = "";
    List<ExecutionParamEntity> emptyList = new ArrayList<>();
    
    when(executionParamRepository.findAllByExecutionId(executionId)).thenReturn(emptyList);

    // Act
    List<ExecutionParamEntity> result = executionParamService.findAllByExecutionId(executionId);

    // Assert
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void findAllByExecutionId_singleParam_returnsListWithOneElement() {
    // Arrange
    String executionId = "single-param-execution-id";
    ExecutionParamEntity singleParam = createTestExecutionParamEntity("singleParam", executionId);
    List<ExecutionParamEntity> singleParamList = Arrays.asList(singleParam);
    
    when(executionParamRepository.findAllByExecutionId(executionId)).thenReturn(singleParamList);

    // Act
    List<ExecutionParamEntity> result = executionParamService.findAllByExecutionId(executionId);

    // Assert
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals(singleParam, result.get(0));
    assertEquals("singleParam", result.get(0).getName());
    assertEquals(executionId, result.get(0).getExecutionId());
  }

  @Test
  void findAllByExecutionId_multipleExecutions_returnsCorrectParams() {
    // Arrange
    String executionId1 = "execution-1";
    String executionId2 = "execution-2";
    
    List<ExecutionParamEntity> paramsForExecution1 = Arrays.asList(
        createTestExecutionParamEntity("param1", executionId1),
        createTestExecutionParamEntity("param2", executionId1)
    );
    
    List<ExecutionParamEntity> paramsForExecution2 = Arrays.asList(
        createTestExecutionParamEntity("param3", executionId2)
    );
    
    when(executionParamRepository.findAllByExecutionId(executionId1)).thenReturn(paramsForExecution1);
    when(executionParamRepository.findAllByExecutionId(executionId2)).thenReturn(paramsForExecution2);

    // Act
    List<ExecutionParamEntity> result1 = executionParamService.findAllByExecutionId(executionId1);
    List<ExecutionParamEntity> result2 = executionParamService.findAllByExecutionId(executionId2);

    // Assert
    assertNotNull(result1);
    assertNotNull(result2);
    assertEquals(2, result1.size());
    assertEquals(1, result2.size());
    
    // Verify execution IDs are correct
    for (ExecutionParamEntity param : result1) {
      assertEquals(executionId1, param.getExecutionId());
    }
    for (ExecutionParamEntity param : result2) {
      assertEquals(executionId2, param.getExecutionId());
    }
  }

  /**
   * Creates a test ExecutionParamEntity for use in tests.
   */
  private ExecutionParamEntity createTestExecutionParamEntity(String name, String executionId) {
    ExecutionParamEntity entity = new ExecutionParamEntity();
    entity.setId("test-param-id-" + name);
    entity.setName(name);
    entity.setExecutionId(executionId);
    return entity;
  }
}
