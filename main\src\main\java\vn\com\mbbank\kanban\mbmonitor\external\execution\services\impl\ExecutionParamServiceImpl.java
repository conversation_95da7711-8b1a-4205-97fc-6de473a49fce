package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionParamEntity;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.ExecutionParamRepository;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionParamService;

@Service
@RequiredArgsConstructor
public class ExecutionParamServiceImpl extends BaseServiceImpl<ExecutionParamEntity, String>
    implements ExecutionParamService {
  private final ExecutionParamRepository executionParamRepository;

  @Override
  protected JpaCommonRepository<ExecutionParamEntity, String> getRepository() {
    return executionParamRepository;
  }

  @Override
  public List<ExecutionParamEntity> findAllByExecutionId(String executionId) {
    return executionParamRepository.findAllByExecutionId(executionId);
  }
}
