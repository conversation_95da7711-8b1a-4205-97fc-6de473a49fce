package vn.com.mbbank.kanban.mbmonitor.external.execution.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionApiEntity;


/**
 * Repository table ExecutionApi.
 */
@Repository
public interface ExecutionApiRepository
    extends JpaCommonRepository<ExecutionApiEntity, String> {
  /**
   * Deletes all records from the database that match the executionId.
   *
   * @param executionId id of execution.
   */
  void deleteAllByExecutionId(String executionId);

  /**
   * find all by executionId.
   *
   * @param executionId for finding
   * @return execution api
   */
  ExecutionApiEntity findAllByExecutionId(String executionId);

  /**
   * find all by list execution id.
   *
   * @param executionIds for find
   * @return list of ExecutionApi
   */
  List<ExecutionApiEntity> findAllByExecutionIdIn(List<String> executionIds);
}
