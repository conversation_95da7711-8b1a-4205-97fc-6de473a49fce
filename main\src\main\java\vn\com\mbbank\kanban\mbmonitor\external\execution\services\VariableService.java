package vn.com.mbbank.kanban.mbmonitor.external.execution.services;

import java.util.List;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;


/**
 * interface logic Variable.
 */
public interface VariableService extends BaseService<VariableEntity, String> {

  /**
   * Find all Variable.
   *
   * @param id id
   * @return page VariableEntity
   */
  VariableEntity findWithId(String id) throws BusinessException;

  /**
   * find variable by names.
   *
   * @param names variable variable name
   * @return list of variable
   */
  List<VariableEntity> findAllByNameIn(List<String> names);
  
  /**
   * Returns execution IDs of variables with type 'DYNAMIC_VALUE'
   * that are referenced by parameters in the given execution.
   *
   * @param executionId the execution ID to check
   * @return list of referenced execution IDs
   */
  List<String> getReferencedDynamicExecutionIds(String executionId);
  
  /**
   * find variable by executionId.
   *
   * @param executionId executionId
   * @return list of variable
   */
  List<VariableEntity> findAllByExecutionId(String executionId);
}