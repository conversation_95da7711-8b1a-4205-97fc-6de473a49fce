package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionHistoryEntity;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.ExecutionHistoryRepository;

@ExtendWith(MockitoExtension.class)
class ExecutionHistoryServiceImplTest {

  @Mock
  private ExecutionHistoryRepository executionHistoryRepository;

  @InjectMocks
  private ExecutionHistoryServiceImpl executionHistoryService;


  @Test
  void getRepository_success_returnsConfiguredRepository() {
    // Act
    JpaCommonRepository<ExecutionHistoryEntity, String> repository = executionHistoryService.getRepository();

    // Assert
    assertNotNull(repository);
    assertSame(executionHistoryRepository, repository, "The returned repository should be the mocked instance.");
  }
}
