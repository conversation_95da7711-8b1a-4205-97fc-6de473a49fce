package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.constants.ExecutionConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.VariableResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionApiEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionParamEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;

/**
 * ExecutionResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ExecutionResponseMapper extends KanbanBaseMapper<ExecutionResponse, ExecutionEntity> {
  ExecutionResponseMapper INSTANCE = Mappers.getMapper(ExecutionResponseMapper.class);

  /**
   * merge request to entity.
   *
   * @param entity    ExecutionEntity
   * @param params    list params
   * @param variables list variable
   * @param executionApiEntity ExecutionApiEntity
   * @return ExecutionResponse
   */
  default ExecutionResponse map(ExecutionEntity entity, List<ExecutionParamEntity> params,
                                List<VariableEntity> variables, ExecutionApiEntity executionApiEntity) {
    if (Objects.isNull(entity)) {
      return null;
    }
    var res = map(entity);
    if (Objects.nonNull(executionApiEntity)) {
      res.setApiInfo(ExecutionApiEntityMapper.INSTANCE.toModel(executionApiEntity));
    } else {
      res.setApiInfo(null);
    }
    if (CollectionUtils.isEmpty(params)) {
      return res;
    }
    var envList = new ArrayList<VariableResponse>();
    var variableMap =
        CollectionUtils.isEmpty(variables) ? new HashMap<String, VariableEntity>() : variables.stream()
            .collect(Collectors.toMap(VariableEntity::getName, Function.identity()));
    for (ExecutionParamEntity param : params) {
      var variable = new VariableResponse();
      var name = param.getName();
      if (variableMap.containsKey(name)) {
        var var = variableMap.get(name);
        variable.setId(var.getId());
        variable.setDescription(var.getDescription());
        variable.setValue(var.isHidden()
            ? ExecutionConstants.HIDDEN_VARIABLE_PLACEHOLDER
            : var.getValue());
        variable.setHidden(var.isHidden());
        variable.setName(var.getName());
        variable.setExecutionId(var.getExecutionId());
        variable.setType(var.getType());
        variable.setDataType(var.getDataType());
        variable.setJsonPath(var.getJsonPath());
        variable.setEnableExpiration(var.isEnableExpiration());
        variable.setExpirationTime(var.getExpirationTime());
      } else {
        variable.setName(name);
      }
      envList.add(variable);
    }
    res.setVariables(envList);
    return res;
  }
}
